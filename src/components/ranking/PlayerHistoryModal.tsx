"use client";

import React from "react";
import { X, Calendar, MapPin, Trophy, Loader2 } from "lucide-react";
import { Player } from "@/types/ranking";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { usePlayerHistory } from "@/lib/hooks/usePlayers";

interface PlayerHistoryModalProps {
  player: Player | null;
  isOpen: boolean;
  onClose: () => void;
}

export function PlayerHistoryModal({ player, isOpen, onClose }: PlayerHistoryModalProps) {
  const { data: playerHistory, isLoading, error } = usePlayerHistory(player?.id || null);

  if (!isOpen || !player) return null;

  // Helper function to format tournament name with season
  const formatTournamentName = (title: string, seasonName?: string) => {
    if (seasonName) {
      return `${title} - ${seasonName}`;
    }
    return title;
  };

  // Helper function to check if position should be displayed (only for top 3)
  const shouldShowPosition = (position: number) => {
    return position >= 1 && position <= 3;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header del Modale */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-lg sm:text-xl font-bold">Storia Tornei - {player.name}</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Contenuto del Modale */}
        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(95vh-8rem)] sm:max-h-[calc(90vh-8rem)]">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-6 w-6 animate-spin text-blue-400" />
              <span className="ml-2 text-blue-300">Caricamento storico...</span>
            </div>
          ) : error ? (
            <div className="text-center p-4 text-red-400">
              Errore nel caricamento dello storico
            </div>
          ) : (
            <div className="space-y-3 sm:space-y-4">
              {!playerHistory || playerHistory.length === 0 ? (
                <div className="text-center p-4 text-blue-300/75">
                  Nessun torneo giocato
                </div>
              ) : (
                playerHistory.map((result) => (
                  <div
                    key={result.id}
                    className="bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 p-3 sm:p-4"
                  >
                    {/* Desktop Layout */}
                    <div className="hidden sm:block">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium mb-2 text-base">
                            {formatTournamentName(
                              result.tournament?.title || 'Torneo',
                              result.tournament?.season?.name
                            )}
                          </h4>
                          <div className="space-y-2 text-sm text-blue-200">
                            <div className="flex items-center gap-2">
                              <MapPin size={14} className="text-blue-400" />
                              <span>{result.tournament?.store?.name || 'Location non disponibile'}</span>
                            </div>
                            {result.archetype && (
                              <div className="flex items-center gap-2">
                                <span className="text-blue-400">🃏</span>
                                <span>{result.archetype.name}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex flex-col items-end ml-4">
                          <div className="text-lg font-bold text-blue-300 mb-1">
                            {result.points} pt
                          </div>
                          {shouldShowPosition(result.position) && (
                            <div className="flex items-center gap-2">
                              <Trophy size={14} className="text-yellow-400" />
                              <span className="font-medium text-sm text-yellow-300">
                                {result.position}° posto
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Mobile Layout */}
                    <div className="block sm:hidden">
                      <h4 className="font-medium mb-2 text-sm">
                        {formatTournamentName(
                          result.tournament?.title || 'Torneo',
                          result.tournament?.season?.name
                        )}
                      </h4>
                      <div className="space-y-1.5 text-xs text-blue-200">
                        {result.archetype && (
                          <div className="flex items-center gap-2">
                            <span className="text-blue-400">🃏</span>
                            <span>{result.archetype.name}</span>
                          </div>
                        )}
                        <div className="flex items-center justify-between">
                          <div className="text-base font-bold text-blue-300">
                            {result.points} pt
                          </div>
                          {shouldShowPosition(result.position) && (
                            <div className="flex items-center gap-1.5">
                              <Trophy size={12} className="text-yellow-400" />
                              <span className="font-medium text-xs text-yellow-300">
                                {result.position}° posto
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Footer del Modale */}
        <div className="p-3 sm:p-4 border-t border-blue-500/20 bg-black/20">
          <div className="flex justify-between items-center">
            <span className="text-blue-200 text-sm sm:text-base">Totale Punti Stagione</span>
            <span className="text-xl sm:text-2xl font-bold">{player.totalPoints} pt</span>
          </div>
        </div>
      </div>
    </div>
  );
} 