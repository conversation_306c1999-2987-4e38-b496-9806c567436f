"use client";

import { useState, useRef, useEffect } from 'react';
import { ChevronDown, User, Grid3X3, LogOut } from 'lucide-react';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import Link from 'next/link';

export function UserDropdown() {
  const { user, player, signOut } = useAuthContext();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  if (!user) return null;

  const displayName = player?.first_name && player?.last_name 
    ? `${player.first_name} ${player.last_name}`
    : user.email?.split('@')[0] || 'Utente';

  const handleSignOut = async () => {
    setIsOpen(false);
    await signOut();
  };

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-3 py-1.5 bg-black/20 backdrop-blur-sm border border-blue-500/30 hover:bg-black/30 rounded-lg text-sm font-medium transition-colors"
      >
        {displayName}
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-1 w-48 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-10">
          <div className="py-1">
            <Link
              href="/profile"
              onClick={() => setIsOpen(false)}
              className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
            >
              <User size={16} />
              Profilo utente
            </Link>
            <Link
              href="/calendar"
              onClick={() => setIsOpen(false)}
              className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
            >
              <Grid3X3 size={16} />
              Schede
            </Link>
            <button
              onClick={handleSignOut}
              className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors text-red-400 hover:text-red-300"
            >
              <LogOut size={16} />
              Disconnetti
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
